#!/usr/bin/env python3
"""
Elastic Net Model for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import ElasticNet, ElasticNetCV
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()
    test_sessions = pd.read_csv('test_sessions.csv')

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")

    return X_train, X_test, y_train, test_sessions

def train_elastic_net_model(X_train, y_train):
    """Train Elastic Net model with optimized alpha and l1_ratio"""
    print("\n" + "=" * 60)
    print("TRAINING ELASTIC NET MODEL")
    print("=" * 60)

    # Scale the features (important for Elastic Net)
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # Split for validation
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train_scaled, y_train, test_size=0.2, random_state=42
    )

    # Use ElasticNetCV to find optimal alpha and l1_ratio
    print("Finding optimal alpha and l1_ratio with cross-validation...")

    # Define parameter ranges
    alphas = [0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 50.0]
    l1_ratios = [0.1, 0.3, 0.5, 0.7, 0.9]  # 0.5 = equal Ridge and Lasso

    # Use ElasticNetCV for efficient cross-validation
    elastic_cv = ElasticNetCV(
        alphas=alphas,
        l1_ratio=l1_ratios,
        cv=5,
        random_state=42,
        max_iter=2000,
        selection='random'
    )

    elastic_cv.fit(X_train_split, y_train_split)

    optimal_alpha = elastic_cv.alpha_
    optimal_l1_ratio = elastic_cv.l1_ratio_

    print(f"Optimal alpha: {optimal_alpha}")
    print(f"Optimal l1_ratio: {optimal_l1_ratio}")
    print(f"L1_ratio interpretation:")
    if optimal_l1_ratio < 0.3:
        print("  → More Ridge-like (L2 regularization dominant)")
    elif optimal_l1_ratio > 0.7:
        print("  → More Lasso-like (L1 regularization dominant)")
    else:
        print("  → Balanced Ridge and Lasso (L1 + L2 regularization)")

    # Train Elastic Net with optimal parameters
    elastic_model = ElasticNet(
        alpha=optimal_alpha,
        l1_ratio=optimal_l1_ratio,
        random_state=42,
        max_iter=2000
    )
    elastic_model.fit(X_train_split, y_train_split)

    # Evaluate on validation set
    y_val_pred = elastic_model.predict(X_val_split)
    val_mse = mean_squared_error(y_val_split, y_val_pred)
    val_r2 = r2_score(y_val_split, y_val_pred)

    print(f"Validation MSE: {val_mse:.4f}")
    print(f"Validation R²: {val_r2:.4f}")

    # Cross-validation score
    cv_scores = cross_val_score(elastic_model, X_train_scaled, y_train, cv=5,
                               scoring='neg_mean_squared_error')
    cv_mse = -cv_scores.mean()
    cv_std = cv_scores.std()

    print(f"Cross-validation MSE: {cv_mse:.4f} (±{cv_std:.4f})")

    # Train final model on full training data
    print("\nTraining final model on full training data...")
    final_model = ElasticNet(
        alpha=optimal_alpha,
        l1_ratio=optimal_l1_ratio,
        random_state=42,
        max_iter=2000
    )
    final_model.fit(X_train_scaled, y_train)

    # Final training evaluation
    y_train_pred = final_model.predict(X_train_scaled)
    train_mse = mean_squared_error(y_train, y_train_pred)
    train_r2 = r2_score(y_train, y_train_pred)

    print(f"Final training MSE: {train_mse:.4f}")
    print(f"Final training R²: {train_r2:.4f}")

    return final_model, scaler, optimal_alpha, optimal_l1_ratio

def analyze_feature_selection_and_coefficients(model, feature_names, l1_ratio):
    """Analyze Elastic Net feature selection and coefficients"""
    print("\n" + "=" * 60)
    print("ELASTIC NET FEATURE ANALYSIS")
    print("=" * 60)

    # Get coefficients
    coefficients = model.coef_

    # Identify selected features (non-zero coefficients)
    selected_features = coefficients != 0
    selected_count = selected_features.sum()
    total_features = len(coefficients)

    print(f"Feature Selection Results:")
    print(f"Total features: {total_features}")
    print(f"Selected features: {selected_count}")
    print(f"Eliminated features: {total_features - selected_count}")
    print(f"Selection ratio: {selected_count/total_features:.2%}")

    # Create feature analysis DataFrame
    feature_analysis_df = pd.DataFrame({
        'feature': feature_names,
        'coefficient': coefficients,
        'abs_coefficient': np.abs(coefficients),
        'selected': selected_features
    }).sort_values('abs_coefficient', ascending=False)

    # Show selected features
    selected_features_df = feature_analysis_df[feature_analysis_df['selected']].copy()

    print(f"\nTop Selected Features by Absolute Coefficient:")
    print(selected_features_df[['feature', 'coefficient', 'abs_coefficient']].head(20).to_string(index=False, float_format='%.4f'))

    # Show eliminated features
    eliminated_features_df = feature_analysis_df[~feature_analysis_df['selected']].copy()

    if len(eliminated_features_df) > 0:
        print(f"\nEliminated Features (coefficient = 0):")
        print(f"Count: {len(eliminated_features_df)}")
        print("Features:", eliminated_features_df['feature'].tolist()[:10], "..." if len(eliminated_features_df) > 10 else "")
    else:
        print(f"\nNo features were eliminated (all coefficients non-zero)")

    # Save feature analysis
    feature_analysis_df.to_csv('elastic_net_feature_analysis.csv', index=False)
    print(f"\nFeature analysis saved to 'elastic_net_feature_analysis.csv'")

    # Analyze positive vs negative coefficients
    positive_coefs = selected_features_df[selected_features_df['coefficient'] > 0]
    negative_coefs = selected_features_df[selected_features_df['coefficient'] < 0]

    print(f"\nSelected Coefficient Analysis:")
    print(f"Positive coefficients: {len(positive_coefs)} features")
    print(f"Negative coefficients: {len(negative_coefs)} features")
    print(f"Intercept: {model.intercept_:.4f}")

    # Compare with pure Ridge and Lasso behavior
    print(f"\nRegularization Analysis (l1_ratio = {l1_ratio:.2f}):")
    ridge_weight = 1 - l1_ratio
    lasso_weight = l1_ratio
    print(f"Ridge (L2) weight: {ridge_weight:.2f}")
    print(f"Lasso (L1) weight: {lasso_weight:.2f}")

    if selected_count < total_features:
        print(f"✅ Feature selection occurred (Lasso effect)")
    else:
        print(f"❌ No feature selection (Ridge-dominant)")

    return feature_analysis_df, selected_features_df

def make_predictions(model, scaler, X_test):
    """Make predictions on test data"""
    print("\n" + "=" * 60)
    print("MAKING PREDICTIONS ON TEST DATA")
    print("=" * 60)

    # Scale test features
    X_test_scaled = scaler.transform(X_test)

    # Make predictions
    predictions = model.predict(X_test_scaled)

    print(f"Generated {len(predictions)} predictions")
    print(f"Prediction statistics:")
    print(f"  Mean: {predictions.mean():.4f}")
    print(f"  Median: {np.median(predictions):.4f}")
    print(f"  Min: {predictions.min():.4f}")
    print(f"  Max: {predictions.max():.4f}")
    print(f"  Std: {predictions.std():.4f}")

    return predictions

def create_submission_file(predictions, test_sessions):
    """Create the final submission file"""
    print("\n" + "=" * 60)
    print("CREATING ELASTIC NET SUBMISSION FILE")
    print("=" * 60)

    # Create submission DataFrame
    submission = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': predictions
    })

    # Ensure predictions are positive (Elastic Net can predict negative values)
    submission['session_value'] = np.maximum(submission['session_value'], 0.1)

    # Sort by user_session
    submission = submission.sort_values('user_session').reset_index(drop=True)

    # Save submission file
    submission.to_csv('elastic_net_submission.csv', index=False)

    print(f"Elastic Net submission file saved as 'elastic_net_submission.csv'")
    print(f"First few predictions:")
    print(submission.head(10))

    # Check how many predictions were clipped to positive
    clipped_count = (predictions <= 0).sum()
    if clipped_count > 0:
        print(f"\nNote: {clipped_count} negative predictions were clipped to 0.1")

    return submission

def compare_with_ridge_and_lasso(optimal_alpha, optimal_l1_ratio):
    """Compare Elastic Net parameters with pure Ridge and Lasso"""
    print("\n" + "=" * 60)
    print("COMPARISON WITH RIDGE AND LASSO")
    print("=" * 60)

    print(f"Elastic Net Parameters:")
    print(f"  Alpha (regularization strength): {optimal_alpha}")
    print(f"  L1_ratio (mixing parameter): {optimal_l1_ratio}")

    print(f"\nEquivalent Models:")
    print(f"  Pure Ridge (l1_ratio=0.0): alpha={optimal_alpha}")
    print(f"  Pure Lasso (l1_ratio=1.0): alpha={optimal_alpha}")
    print(f"  Current Elastic Net: alpha={optimal_alpha}, l1_ratio={optimal_l1_ratio}")

    # Calculate effective regularization
    ridge_component = optimal_alpha * (1 - optimal_l1_ratio)
    lasso_component = optimal_alpha * optimal_l1_ratio

    print(f"\nEffective Regularization:")
    print(f"  Ridge (L2) component: {ridge_component:.4f}")
    print(f"  Lasso (L1) component: {lasso_component:.4f}")

    if optimal_l1_ratio < 0.1:
        recommendation = "Consider using Ridge Regression instead"
    elif optimal_l1_ratio > 0.9:
        recommendation = "Consider using Lasso Regression instead"
    else:
        recommendation = "Elastic Net is providing good balance between Ridge and Lasso"

    print(f"\nRecommendation: {recommendation}")

def main():
    """Main Elastic Net pipeline"""
    print("Starting Elastic Net Model Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train, test_sessions = load_processed_data()

    # Train Elastic Net model
    elastic_model, scaler, optimal_alpha, optimal_l1_ratio = train_elastic_net_model(X_train, y_train)

    # Analyze feature selection and coefficients
    feature_analysis_df, selected_features_df = analyze_feature_selection_and_coefficients(
        elastic_model, X_train.columns, optimal_l1_ratio
    )

    # Compare with Ridge and Lasso
    compare_with_ridge_and_lasso(optimal_alpha, optimal_l1_ratio)

    # Make predictions on test data
    predictions = make_predictions(elastic_model, scaler, X_test)

    # Create submission file
    submission = create_submission_file(predictions, test_sessions)

    print("\n" + "=" * 80)
    print("ELASTIC NET MODEL COMPLETED")
    print(f"📁 File to submit: elastic_net_submission.csv")
    print(f"📊 Feature analysis: elastic_net_feature_analysis.csv")
    print(f"🎯 Optimal alpha: {optimal_alpha}")
    print(f"⚖️ Optimal l1_ratio: {optimal_l1_ratio}")
    print(f"🔍 Selected features: {len(selected_features_df)}/{len(X_train.columns)}")
    print("=" * 80)

    return elastic_model, submission, feature_analysis_df, selected_features_df, optimal_alpha, optimal_l1_ratio

if __name__ == "__main__":
    elastic_model, submission, feature_analysis_df, selected_features_df, optimal_alpha, optimal_l1_ratio = main()