#!/usr/bin/env python3
"""
Hyperparameter Tuning & Cross-validation for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV, KFold
from sklearn.preprocessing import StandardScaler
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
from scipy.stats import uniform, randint
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")

    return X_train, X_test, y_train

def setup_cross_validation():
    """Setup cross-validation strategy"""
    # Use 5-fold cross-validation
    cv = KFold(n_splits=5, shuffle=True, random_state=42)
    return cv

def tune_neural_network(X_train, y_train, cv):
    """Tune Neural Network hyperparameters"""
    print("\n" + "=" * 60)
    print("TUNING NEURAL NETWORK")
    print("=" * 60)

    # Scale the features for neural network
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # Define parameter grid
    param_grid = {
        'hidden_layer_sizes': [
            (64, 32), (128, 64), (128, 64, 32), (256, 128, 64),
            (100, 50), (200, 100, 50)
        ],
        'alpha': [0.0001, 0.001, 0.01, 0.1],
        'learning_rate_init': [0.001, 0.01, 0.1],
        'max_iter': [500, 1000]
    }

    # Create base model
    nn_model = MLPRegressor(
        activation='relu',
        solver='adam',
        batch_size='auto',
        learning_rate='constant',
        random_state=42,
        early_stopping=True,
        validation_fraction=0.1,
        n_iter_no_change=10
    )

    # Perform grid search
    print("Performing Grid Search for Neural Network...")
    grid_search = GridSearchCV(
        nn_model, param_grid, cv=cv, scoring='neg_mean_squared_error',
        n_jobs=-1, verbose=1
    )

    grid_search.fit(X_train_scaled, y_train)

    print(f"Best Neural Network parameters: {grid_search.best_params_}")
    print(f"Best Neural Network CV score: {-grid_search.best_score_:.4f}")

    return grid_search.best_estimator_, scaler, -grid_search.best_score_

def tune_xgboost(X_train, y_train, cv):
    """Tune XGBoost hyperparameters"""
    print("\n" + "=" * 60)
    print("TUNING XGBOOST")
    print("=" * 60)

    # Define parameter distributions for RandomizedSearchCV
    param_distributions = {
        'n_estimators': randint(100, 500),
        'max_depth': randint(3, 10),
        'learning_rate': uniform(0.01, 0.3),
        'subsample': uniform(0.6, 0.4),
        'colsample_bytree': uniform(0.6, 0.4),
        'reg_alpha': uniform(0, 1),
        'reg_lambda': uniform(0, 1)
    }

    # Create base model
    xgb_model = xgb.XGBRegressor(random_state=42, n_jobs=-1)

    # Perform randomized search
    print("Performing Randomized Search for XGBoost...")
    random_search = RandomizedSearchCV(
        xgb_model, param_distributions, n_iter=50, cv=cv,
        scoring='neg_mean_squared_error', n_jobs=-1, verbose=1, random_state=42
    )

    random_search.fit(X_train, y_train)

    print(f"Best XGBoost parameters: {random_search.best_params_}")
    print(f"Best XGBoost CV score: {-random_search.best_score_:.4f}")

    return random_search.best_estimator_, -random_search.best_score_

def tune_lightgbm(X_train, y_train, cv):
    """Tune LightGBM hyperparameters"""
    print("\n" + "=" * 60)
    print("TUNING LIGHTGBM")
    print("=" * 60)

    # Define parameter distributions for RandomizedSearchCV
    param_distributions = {
        'n_estimators': randint(100, 500),
        'max_depth': randint(3, 10),
        'learning_rate': uniform(0.01, 0.3),
        'subsample': uniform(0.6, 0.4),
        'colsample_bytree': uniform(0.6, 0.4),
        'reg_alpha': uniform(0, 1),
        'reg_lambda': uniform(0, 1),
        'num_leaves': randint(20, 100)
    }

    # Create base model
    lgb_model = lgb.LGBMRegressor(random_state=42, n_jobs=-1, verbose=-1)

    # Perform randomized search
    print("Performing Randomized Search for LightGBM...")
    random_search = RandomizedSearchCV(
        lgb_model, param_distributions, n_iter=50, cv=cv,
        scoring='neg_mean_squared_error', n_jobs=-1, verbose=1, random_state=42
    )

    random_search.fit(X_train, y_train)

    print(f"Best LightGBM parameters: {random_search.best_params_}")
    print(f"Best LightGBM CV score: {-random_search.best_score_:.4f}")

    return random_search.best_estimator_, -random_search.best_score_

def evaluate_tuned_models(models, X_train, X_test, y_train):
    """Evaluate all tuned models"""
    print("\n" + "=" * 60)
    print("EVALUATING TUNED MODELS")
    print("=" * 60)

    results = []

    for model_name, model_info in models.items():
        print(f"\nEvaluating {model_name}...")

        model = model_info['model']
        cv_score = model_info['cv_score']

        # Handle scaling for neural network
        if model_name == 'Neural Network':
            scaler = model_info['scaler']
            X_train_processed = scaler.transform(X_train)
            X_test_processed = scaler.transform(X_test)
        else:
            X_train_processed = X_train
            X_test_processed = X_test

        # Train on full training set
        model.fit(X_train_processed, y_train)

        # Make predictions
        y_train_pred = model.predict(X_train_processed)

        # Calculate metrics
        train_mse = mean_squared_error(y_train, y_train_pred)
        train_r2 = r2_score(y_train, y_train_pred)

        results.append({
            'Model': model_name,
            'CV MSE': cv_score,
            'Train MSE': train_mse,
            'Train R²': train_r2
        })

        print(f"{model_name} - CV MSE: {cv_score:.4f}, Train MSE: {train_mse:.4f}, Train R²: {train_r2:.4f}")

    return results

def main():
    """Main hyperparameter tuning pipeline"""
    print("Starting Hyperparameter Tuning & Cross-validation Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train = load_processed_data()

    # Setup cross-validation
    cv = setup_cross_validation()

    # Dictionary to store tuned models
    tuned_models = {}

    # Tune Neural Network (best performing model from initial training)
    nn_model, nn_scaler, nn_cv_score = tune_neural_network(X_train, y_train, cv)
    tuned_models['Neural Network'] = {
        'model': nn_model,
        'scaler': nn_scaler,
        'cv_score': nn_cv_score
    }

    # Tune XGBoost (second best performing)
    xgb_model, xgb_cv_score = tune_xgboost(X_train, y_train, cv)
    tuned_models['XGBoost'] = {
        'model': xgb_model,
        'cv_score': xgb_cv_score
    }

    # Tune LightGBM (third best performing)
    lgb_model, lgb_cv_score = tune_lightgbm(X_train, y_train, cv)
    tuned_models['LightGBM'] = {
        'model': lgb_model,
        'cv_score': lgb_cv_score
    }

    # Evaluate all tuned models
    results = evaluate_tuned_models(tuned_models, X_train, X_test, y_train)

    # Create results DataFrame and save
    results_df = pd.DataFrame(results)
    results_df = results_df.sort_values('CV MSE')

    print("\n" + "=" * 60)
    print("FINAL TUNED MODEL COMPARISON")
    print("=" * 60)
    print(results_df.to_string(index=False, float_format='%.4f'))

    # Select best tuned model
    best_model_name = results_df.iloc[0]['Model']
    best_model_info = tuned_models[best_model_name]
    best_model = best_model_info['model']

    print(f"\nBest Tuned Model: {best_model_name}")
    print(f"Best CV MSE: {results_df.iloc[0]['CV MSE']:.4f}")

    # Save results
    print("\n" + "=" * 60)
    print("SAVING RESULTS")
    print("=" * 60)

    results_df.to_csv('tuned_model_comparison.csv', index=False)

    # Save the best model parameters
    if best_model_name == 'Neural Network':
        model_params = {
            'model_type': 'Neural Network',
            'hidden_layer_sizes': best_model.hidden_layer_sizes,
            'alpha': best_model.alpha,
            'learning_rate_init': best_model.learning_rate_init,
            'max_iter': best_model.max_iter
        }
    elif best_model_name == 'XGBoost':
        model_params = {
            'model_type': 'XGBoost',
            'n_estimators': best_model.n_estimators,
            'max_depth': best_model.max_depth,
            'learning_rate': best_model.learning_rate,
            'subsample': best_model.subsample,
            'colsample_bytree': best_model.colsample_bytree,
            'reg_alpha': best_model.reg_alpha,
            'reg_lambda': best_model.reg_lambda
        }
    elif best_model_name == 'LightGBM':
        model_params = {
            'model_type': 'LightGBM',
            'n_estimators': best_model.n_estimators,
            'max_depth': best_model.max_depth,
            'learning_rate': best_model.learning_rate,
            'subsample': best_model.subsample,
            'colsample_bytree': best_model.colsample_bytree,
            'reg_alpha': best_model.reg_alpha,
            'reg_lambda': best_model.reg_lambda,
            'num_leaves': best_model.num_leaves
        }

    # Save model parameters
    pd.Series(model_params).to_csv('best_model_params.csv')

    print("Results saved:")
    print("- tuned_model_comparison.csv")
    print("- best_model_params.csv")

    print("\n" + "=" * 80)
    print("HYPERPARAMETER TUNING COMPLETED")
    print(f"Best Tuned Model: {best_model_name}")
    print("=" * 80)

    return best_model, best_model_name, tuned_models, X_train, X_test, y_train

if __name__ == "__main__":
    best_model, best_model_name, tuned_models, X_train, X_test, y_train = main()