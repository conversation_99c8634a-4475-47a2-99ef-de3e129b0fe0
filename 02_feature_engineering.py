#!/usr/bin/env python3
"""
Feature Engineering for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load and prepare the data"""
    print("=" * 60)
    print("LOADING DATA")
    print("=" * 60)

    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')

    # Convert event_time to datetime
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])
    test_df['event_time'] = pd.to_datetime(test_df['event_time'])

    print(f"Train shape: {train_df.shape}")
    print(f"Test shape: {test_df.shape}")

    return train_df, test_df

def create_temporal_features(df):
    """Create time-based features"""
    print("\nCreating temporal features...")

    # Extract temporal components
    df['hour'] = df['event_time'].dt.hour
    df['day_of_week'] = df['event_time'].dt.dayofweek
    df['day_of_month'] = df['event_time'].dt.day
    df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)

    # Time of day categories
    df['time_of_day'] = pd.cut(df['hour'],
                               bins=[0, 6, 12, 18, 24],
                               labels=['night', 'morning', 'afternoon', 'evening'],
                               include_lowest=True)

    return df

def create_session_level_features(df, is_train=True):
    """Create session-level aggregated features"""
    print("\nCreating session-level features...")

    # Basic session aggregations
    session_features = df.groupby('user_session').agg({
        'event_time': ['count', 'min', 'max'],
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first',
        'hour': ['min', 'max', 'mean'],
        'day_of_week': 'first',
        'is_weekend': 'first'
    })

    # Flatten column names
    session_features.columns = [
        'event_count', 'first_event_time', 'last_event_time',
        'unique_products', 'unique_categories', 'user_id',
        'min_hour', 'max_hour', 'avg_hour',
        'day_of_week', 'is_weekend'
    ]

    # Calculate session duration in minutes
    session_features['session_duration_minutes'] = (
        session_features['last_event_time'] - session_features['first_event_time']
    ).dt.total_seconds() / 60

    # Handle single-event sessions (duration = 0)
    session_features['session_duration_minutes'] = session_features['session_duration_minutes'].fillna(0)

    # Hour range within session
    session_features['hour_range'] = session_features['max_hour'] - session_features['min_hour']

    # Event type features
    event_type_features = df.groupby('user_session')['event_type'].apply(
        lambda x: pd.Series({
            'view_count': (x == 'VIEW').sum(),
            'add_cart_count': (x == 'ADD_CART').sum(),
            'remove_cart_count': (x == 'REMOVE_CART').sum(),
            'buy_count': (x == 'BUY').sum()
        })
    ).unstack()

    # Combine features
    session_features = session_features.join(event_type_features, how='left')
    session_features = session_features.fillna(0)

    # Event type ratios
    session_features['view_ratio'] = session_features['view_count'] / session_features['event_count']
    session_features['add_cart_ratio'] = session_features['add_cart_count'] / session_features['event_count']
    session_features['remove_cart_ratio'] = session_features['remove_cart_count'] / session_features['event_count']
    session_features['buy_ratio'] = session_features['buy_count'] / session_features['event_count']

    # Conversion indicators
    session_features['has_view'] = (session_features['view_count'] > 0).astype(int)
    session_features['has_add_cart'] = (session_features['add_cart_count'] > 0).astype(int)
    session_features['has_remove_cart'] = (session_features['remove_cart_count'] > 0).astype(int)
    session_features['has_buy'] = (session_features['buy_count'] > 0).astype(int)

    # Funnel progression features
    session_features['view_to_cart'] = (session_features['has_view'] & session_features['has_add_cart']).astype(int)
    session_features['cart_to_buy'] = (session_features['has_add_cart'] & session_features['has_buy']).astype(int)
    session_features['view_to_buy'] = (session_features['has_view'] & session_features['has_buy']).astype(int)

    # Add target variable for training data
    if is_train:
        session_targets = df.groupby('user_session')['session_value'].first()
        session_features = session_features.join(session_targets, how='left')

    return session_features

def create_user_level_features(df, session_features):
    """Create user-level aggregated features"""
    print("\nCreating user-level features...")

    # User behavior aggregations
    user_features = session_features.groupby('user_id').agg({
        'event_count': ['mean', 'sum', 'std'],
        'unique_products': ['mean', 'sum'],
        'unique_categories': ['mean', 'sum'],
        'session_duration_minutes': ['mean', 'sum'],
        'view_count': ['mean', 'sum'],
        'add_cart_count': ['mean', 'sum'],
        'remove_cart_count': ['mean', 'sum'],
        'buy_count': ['mean', 'sum'],
        'has_buy': 'sum'  # Total sessions with purchases
    })

    # Flatten column names
    user_features.columns = [
        'avg_events_per_session', 'total_events', 'std_events_per_session',
        'avg_products_per_session', 'total_unique_products',
        'avg_categories_per_session', 'total_unique_categories',
        'avg_session_duration', 'total_session_duration',
        'avg_views_per_session', 'total_views',
        'avg_add_carts_per_session', 'total_add_carts',
        'avg_remove_carts_per_session', 'total_remove_carts',
        'avg_buys_per_session', 'total_buys',
        'sessions_with_purchase'
    ]

    # Fill NaN values for std
    user_features['std_events_per_session'] = user_features['std_events_per_session'].fillna(0)

    # User session count
    user_session_counts = session_features.groupby('user_id').size()
    user_features['total_sessions'] = user_session_counts

    # User conversion rates
    user_features['user_conversion_rate'] = user_features['sessions_with_purchase'] / user_features['total_sessions']

    # Join back to session features
    session_features = session_features.join(user_features, on='user_id', how='left')

    return session_features

def create_product_category_features(df, session_features):
    """Create product and category-based features"""
    print("\nCreating product and category features...")

    # Product popularity features
    product_stats = df.groupby('product_id').agg({
        'user_session': 'nunique',
        'event_type': 'count'
    }).rename(columns={'user_session': 'product_session_count', 'event_type': 'product_event_count'})

    # Category popularity features
    category_stats = df.groupby('category_id').agg({
        'user_session': 'nunique',
        'event_type': 'count'
    }).rename(columns={'user_session': 'category_session_count', 'event_type': 'category_event_count'})

    # Most frequent product and category per session
    session_product_stats = df.groupby('user_session').agg({
        'product_id': lambda x: x.value_counts().index[0],  # Most frequent product
        'category_id': lambda x: x.value_counts().index[0]  # Most frequent category
    }).rename(columns={'product_id': 'most_frequent_product', 'category_id': 'most_frequent_category'})

    # Join product stats to session level
    session_features = session_features.join(session_product_stats, how='left')

    # Add product and category popularity scores
    session_features = session_features.join(
        product_stats, on='most_frequent_product', how='left'
    ).join(
        category_stats, on='most_frequent_category', how='left'
    )

    # Fill missing values
    session_features[['product_session_count', 'product_event_count',
                     'category_session_count', 'category_event_count']] = session_features[
        ['product_session_count', 'product_event_count',
         'category_session_count', 'category_event_count']].fillna(0)

    return session_features

def create_interaction_features(session_features):
    """Create interaction and derived features"""
    print("\nCreating interaction features...")

    # Events per minute (activity intensity)
    session_features['events_per_minute'] = session_features['event_count'] / (session_features['session_duration_minutes'] + 1)

    # Product diversity
    session_features['product_diversity'] = session_features['unique_products'] / session_features['event_count']
    session_features['category_diversity'] = session_features['unique_categories'] / session_features['event_count']

    # Cart abandonment indicators
    session_features['cart_abandonment'] = (
        (session_features['add_cart_count'] > 0) & (session_features['buy_count'] == 0)
    ).astype(int)

    # Browse vs action ratio
    session_features['action_ratio'] = (
        session_features['add_cart_count'] + session_features['buy_count']
    ) / session_features['event_count']

    # Remove to add ratio (indecision indicator)
    session_features['remove_to_add_ratio'] = session_features['remove_cart_count'] / (session_features['add_cart_count'] + 1)

    # User engagement score (combination of multiple factors)
    session_features['engagement_score'] = (
        session_features['event_count'] * 0.3 +
        session_features['unique_products'] * 0.2 +
        session_features['session_duration_minutes'] * 0.1 +
        session_features['buy_count'] * 0.4
    )

    # Time-based interaction features
    session_features['weekend_activity'] = session_features['is_weekend'] * session_features['event_count']
    session_features['peak_hour_activity'] = (
        ((session_features['avg_hour'] >= 10) & (session_features['avg_hour'] <= 15)).astype(int) *
        session_features['event_count']
    )

    return session_features

def encode_categorical_features(session_features, categorical_cols=None):
    """Encode categorical features"""
    print("\nEncoding categorical features...")

    if categorical_cols is None:
        categorical_cols = ['day_of_week', 'most_frequent_product', 'most_frequent_category']

    # Label encode categorical features
    label_encoders = {}
    for col in categorical_cols:
        if col in session_features.columns:
            le = LabelEncoder()
            session_features[f'{col}_encoded'] = le.fit_transform(session_features[col].astype(str))
            label_encoders[col] = le

    return session_features, label_encoders

def prepare_features_for_modeling(session_features, is_train=True):
    """Prepare final feature set for modeling"""
    print("\nPreparing features for modeling...")

    # Define feature columns (exclude non-feature columns)
    exclude_cols = ['user_id', 'first_event_time', 'last_event_time',
                   'most_frequent_product', 'most_frequent_category']

    if is_train:
        exclude_cols.append('session_value')
        target = session_features['session_value']
    else:
        target = None

    # Select feature columns
    feature_cols = [col for col in session_features.columns if col not in exclude_cols]
    features = session_features[feature_cols]

    # Handle any remaining missing values
    features = features.fillna(0)

    # Handle infinite values
    features = features.replace([np.inf, -np.inf], 0)

    print(f"Final feature set shape: {features.shape}")
    print(f"Feature columns: {len(feature_cols)}")

    return features, target, feature_cols

def main():
    """Main feature engineering pipeline"""
    print("Starting Feature Engineering Pipeline")
    print("=" * 80)

    # Load data
    train_df, test_df = load_data()

    # Create temporal features
    train_df = create_temporal_features(train_df)
    test_df = create_temporal_features(test_df)

    # Create session-level features
    print("\n" + "=" * 60)
    print("PROCESSING TRAINING DATA")
    print("=" * 60)
    train_session_features = create_session_level_features(train_df, is_train=True)

    print("\n" + "=" * 60)
    print("PROCESSING TEST DATA")
    print("=" * 60)
    test_session_features = create_session_level_features(test_df, is_train=False)

    # Create user-level features
    train_session_features = create_user_level_features(train_df, train_session_features)
    test_session_features = create_user_level_features(test_df, test_session_features)

    # Create product and category features
    # Combine train and test for consistent encoding
    combined_df = pd.concat([train_df, test_df], ignore_index=True)

    train_session_features = create_product_category_features(combined_df, train_session_features)
    test_session_features = create_product_category_features(combined_df, test_session_features)

    # Create interaction features
    train_session_features = create_interaction_features(train_session_features)
    test_session_features = create_interaction_features(test_session_features)

    # Encode categorical features
    train_session_features, label_encoders = encode_categorical_features(train_session_features)
    test_session_features, _ = encode_categorical_features(test_session_features)

    # Prepare final features
    X_train, y_train, feature_cols = prepare_features_for_modeling(train_session_features, is_train=True)
    X_test, _, _ = prepare_features_for_modeling(test_session_features, is_train=False)

    # Ensure test features match train features
    missing_cols = set(X_train.columns) - set(X_test.columns)
    for col in missing_cols:
        X_test[col] = 0

    X_test = X_test[X_train.columns]  # Ensure same column order

    # Save processed data
    print("\n" + "=" * 60)
    print("SAVING PROCESSED DATA")
    print("=" * 60)

    # Save features
    X_train.to_csv('X_train_features.csv', index=True)
    X_test.to_csv('X_test_features.csv', index=True)
    y_train.to_csv('y_train.csv', index=True)

    # Save session mappings for submission
    test_sessions = test_session_features.index
    pd.Series(test_sessions, name='user_session').to_csv('test_sessions.csv', index=False)

    print(f"Training features saved: {X_train.shape}")
    print(f"Test features saved: {X_test.shape}")
    print(f"Training targets saved: {y_train.shape}")

    # Feature importance preview
    print(f"\nFeature columns ({len(feature_cols)}):")
    for i, col in enumerate(feature_cols[:20]):  # Show first 20
        print(f"{i+1:2d}. {col}")
    if len(feature_cols) > 20:
        print(f"... and {len(feature_cols) - 20} more features")

    print("\n" + "=" * 80)
    print("FEATURE ENGINEERING COMPLETED")
    print("Generated files:")
    print("- X_train_features.csv")
    print("- X_test_features.csv")
    print("- y_train.csv")
    print("- test_sessions.csv")
    print("=" * 80)

    return X_train, X_test, y_train, feature_cols

if __name__ == "__main__":
    X_train, X_test, y_train, feature_cols = main()