#!/usr/bin/env python3
"""
Gradient Boosting Model for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score, validation_curve
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()
    test_sessions = pd.read_csv('test_sessions.csv')

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")

    return X_train, X_test, y_train, test_sessions

def train_gradient_boosting_model(X_train, y_train):
    """Train Gradient Boosting model with optimized parameters"""
    print("\n" + "=" * 60)
    print("TRAINING GRADIENT BOOSTING MODEL")
    print("=" * 60)

    # Split for validation
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )

    # Train baseline Gradient Boosting
    print("Training baseline Gradient Boosting...")
    gb_baseline = GradientBoostingRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=3,
        random_state=42
    )
    gb_baseline.fit(X_train_split, y_train_split)

    # Evaluate baseline
    y_val_pred_baseline = gb_baseline.predict(X_val_split)
    val_mse_baseline = mean_squared_error(y_val_split, y_val_pred_baseline)
    val_r2_baseline = r2_score(y_val_split, y_val_pred_baseline)

    print(f"Baseline - Validation MSE: {val_mse_baseline:.4f}")
    print(f"Baseline - Validation R²: {val_r2_baseline:.4f}")

    # Optimize number of estimators with validation curve
    print("\nOptimizing number of estimators...")
    n_estimators_range = [100, 200, 300, 400, 500]
    train_scores, val_scores = validation_curve(
        GradientBoostingRegressor(learning_rate=0.1, max_depth=6, random_state=42),
        X_train_split, y_train_split,
        param_name='n_estimators',
        param_range=n_estimators_range,
        cv=3, scoring='neg_mean_squared_error', n_jobs=-1
    )

    # Find best n_estimators
    val_scores_mean = -val_scores.mean(axis=1)
    best_n_estimators = n_estimators_range[np.argmin(val_scores_mean)]
    best_val_score = val_scores_mean.min()

    print(f"Best n_estimators: {best_n_estimators}")
    print(f"Best validation MSE: {best_val_score:.4f}")

    # Train optimized Gradient Boosting
    print("\nTraining optimized Gradient Boosting...")
    gb_optimized = GradientBoostingRegressor(
        n_estimators=best_n_estimators,
        learning_rate=0.1,
        max_depth=6,
        min_samples_split=10,
        min_samples_leaf=4,
        subsample=0.8,
        max_features='sqrt',
        random_state=42
    )

    gb_optimized.fit(X_train_split, y_train_split)

    # Evaluate optimized model
    y_val_pred = gb_optimized.predict(X_val_split)
    val_mse = mean_squared_error(y_val_split, y_val_pred)
    val_r2 = r2_score(y_val_split, y_val_pred)

    print(f"Optimized - Validation MSE: {val_mse:.4f}")
    print(f"Optimized - Validation R²: {val_r2:.4f}")

    # Cross-validation score
    print("Performing 5-fold cross-validation...")
    cv_scores = cross_val_score(gb_optimized, X_train, y_train, cv=5,
                               scoring='neg_mean_squared_error', n_jobs=-1)
    cv_mse = -cv_scores.mean()
    cv_std = cv_scores.std()

    print(f"Cross-validation MSE: {cv_mse:.4f} (±{cv_std:.4f})")

    # Train final model on full training data
    print("\nTraining final model on full training data...")
    final_model = GradientBoostingRegressor(
        n_estimators=best_n_estimators,
        learning_rate=0.1,
        max_depth=6,
        min_samples_split=10,
        min_samples_leaf=4,
        subsample=0.8,
        max_features='sqrt',
        random_state=42
    )

    final_model.fit(X_train, y_train)

    # Final training evaluation
    y_train_pred = final_model.predict(X_train)
    train_mse = mean_squared_error(y_train, y_train_pred)
    train_r2 = r2_score(y_train, y_train_pred)

    print(f"Final training MSE: {train_mse:.4f}")
    print(f"Final training R²: {train_r2:.4f}")

    return final_model, best_n_estimators

def analyze_feature_importance(model, feature_names):
    """Analyze Gradient Boosting feature importance"""
    print("\n" + "=" * 60)
    print("GRADIENT BOOSTING FEATURE IMPORTANCE ANALYSIS")
    print("=" * 60)

    # Get feature importance
    importance = model.feature_importances_

    # Create feature importance DataFrame
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    }).sort_values('importance', ascending=False)

    print("Top 20 Most Important Features:")
    print(feature_importance_df.head(20).to_string(index=False, float_format='%.4f'))

    # Save feature importance
    feature_importance_df.to_csv('gradient_boosting_feature_importance.csv', index=False)
    print("\nFeature importance saved to 'gradient_boosting_feature_importance.csv'")

    # Analyze feature importance distribution
    cumulative_importance = feature_importance_df['importance'].cumsum()

    # Find how many features contribute to 80% and 95% of importance
    features_80 = (cumulative_importance <= 0.8).sum() + 1
    features_95 = (cumulative_importance <= 0.95).sum() + 1

    print(f"\nFeature Importance Analysis:")
    print(f"Top {features_80} features contribute to 80% of importance")
    print(f"Top {features_95} features contribute to 95% of importance")
    print(f"Most important feature: {feature_importance_df.iloc[0]['feature']} ({feature_importance_df.iloc[0]['importance']:.4f})")

    return feature_importance_df

def analyze_learning_curve(model, X_train, y_train):
    """Analyze learning curve and training progress"""
    print("\n" + "=" * 60)
    print("LEARNING CURVE ANALYSIS")
    print("=" * 60)

    # Get training scores for each boosting iteration
    train_scores = []
    val_scores = []

    # Split data for learning curve
    X_train_lc, X_val_lc, y_train_lc, y_val_lc = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )

    # Create a model for learning curve analysis
    gb_lc = GradientBoostingRegressor(
        n_estimators=model.n_estimators,
        learning_rate=model.learning_rate,
        max_depth=model.max_depth,
        random_state=42
    )

    gb_lc.fit(X_train_lc, y_train_lc)

    # Calculate staged predictions
    train_predictions = list(gb_lc.staged_predict(X_train_lc))
    val_predictions = list(gb_lc.staged_predict(X_val_lc))

    for i, (train_pred, val_pred) in enumerate(zip(train_predictions, val_predictions)):
        train_mse = mean_squared_error(y_train_lc, train_pred)
        val_mse = mean_squared_error(y_val_lc, val_pred)
        train_scores.append(train_mse)
        val_scores.append(val_mse)

    # Find best iteration
    best_iteration = np.argmin(val_scores) + 1
    best_val_mse = min(val_scores)

    print(f"Best iteration: {best_iteration}")
    print(f"Best validation MSE: {best_val_mse:.4f}")
    print(f"Final training MSE: {train_scores[-1]:.4f}")
    print(f"Final validation MSE: {val_scores[-1]:.4f}")

    # Check for overfitting
    if val_scores[-1] > best_val_mse * 1.05:
        print("⚠️  Potential overfitting detected!")
    else:
        print("✅ No significant overfitting detected")

    return train_scores, val_scores, best_iteration

def make_predictions(model, X_test):
    """Make predictions on test data"""
    print("\n" + "=" * 60)
    print("MAKING PREDICTIONS ON TEST DATA")
    print("=" * 60)

    # Make predictions
    predictions = model.predict(X_test)

    print(f"Generated {len(predictions)} predictions")
    print(f"Prediction statistics:")
    print(f"  Mean: {predictions.mean():.4f}")
    print(f"  Median: {np.median(predictions):.4f}")
    print(f"  Min: {predictions.min():.4f}")
    print(f"  Max: {predictions.max():.4f}")
    print(f"  Std: {predictions.std():.4f}")

    return predictions

def create_submission_file(predictions, test_sessions):
    """Create the final submission file"""
    print("\n" + "=" * 60)
    print("CREATING GRADIENT BOOSTING SUBMISSION FILE")
    print("=" * 60)

    # Create submission DataFrame
    submission = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': predictions
    })

    # Gradient Boosting typically doesn't predict negative values, but let's be safe
    submission['session_value'] = np.maximum(submission['session_value'], 0.1)

    # Sort by user_session
    submission = submission.sort_values('user_session').reset_index(drop=True)

    # Save submission file
    submission.to_csv('gradient_boosting_submission.csv', index=False)

    print(f"Gradient Boosting submission file saved as 'gradient_boosting_submission.csv'")
    print(f"First few predictions:")
    print(submission.head(10))

    # Check how many predictions were clipped to positive
    clipped_count = (predictions <= 0).sum()
    if clipped_count > 0:
        print(f"\nNote: {clipped_count} negative predictions were clipped to 0.1")
    else:
        print("\nAll predictions were positive (as expected for Gradient Boosting)")

    return submission

def main():
    """Main Gradient Boosting pipeline"""
    print("Starting Gradient Boosting Model Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train, test_sessions = load_processed_data()

    # Train Gradient Boosting model
    gb_model, best_n_estimators = train_gradient_boosting_model(X_train, y_train)

    # Analyze feature importance
    feature_importance_df = analyze_feature_importance(gb_model, X_train.columns)

    # Analyze learning curve
    train_scores, val_scores, best_iteration = analyze_learning_curve(gb_model, X_train, y_train)

    # Make predictions on test data
    predictions = make_predictions(gb_model, X_test)

    # Create submission file
    submission = create_submission_file(predictions, test_sessions)

    print("\n" + "=" * 80)
    print("GRADIENT BOOSTING MODEL COMPLETED")
    print(f"📁 File to submit: gradient_boosting_submission.csv")
    print(f"📊 Feature importance: gradient_boosting_feature_importance.csv")
    print(f"🚀 Best n_estimators: {best_n_estimators}")
    print(f"🎯 Best iteration: {best_iteration}")
    print("=" * 80)

    return gb_model, submission, feature_importance_df, best_n_estimators

if __name__ == "__main__":
    gb_model, submission, feature_importance_df, best_n_estimators = main()