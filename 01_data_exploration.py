#!/usr/bin/env python3
"""
Data Exploration & Understanding for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")

def load_and_basic_info():
    """Load data and display basic information"""
    print("=" * 60)
    print("LOADING AND BASIC DATA INFORMATION")
    print("=" * 60)

    # Load datasets
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_sub = pd.read_csv('sample_submission.csv')

    print(f"Train dataset shape: {train_df.shape}")
    print(f"Test dataset shape: {test_df.shape}")
    print(f"Sample submission shape: {sample_sub.shape}")

    print("\nTrain dataset columns:")
    print(train_df.columns.tolist())

    print("\nTrain dataset info:")
    print(train_df.info())

    print("\nFirst few rows of train data:")
    print(train_df.head())

    print("\nBasic statistics for numerical columns:")
    print(train_df.describe())

    return train_df, test_df, sample_sub

def analyze_target_variable(train_df):
    """Analyze the target variable (session_value)"""
    print("\n" + "=" * 60)
    print("TARGET VARIABLE ANALYSIS (session_value)")
    print("=" * 60)

    # Basic statistics
    print("Session Value Statistics:")
    print(f"Mean: {train_df['session_value'].mean():.2f}")
    print(f"Median: {train_df['session_value'].median():.2f}")
    print(f"Std: {train_df['session_value'].std():.2f}")
    print(f"Min: {train_df['session_value'].min():.2f}")
    print(f"Max: {train_df['session_value'].max():.2f}")
    print(f"Skewness: {train_df['session_value'].skew():.2f}")
    print(f"Kurtosis: {train_df['session_value'].kurtosis():.2f}")

    # Distribution analysis
    plt.figure(figsize=(15, 5))

    plt.subplot(1, 3, 1)
    plt.hist(train_df['session_value'], bins=50, alpha=0.7, edgecolor='black')
    plt.title('Session Value Distribution')
    plt.xlabel('Session Value')
    plt.ylabel('Frequency')

    plt.subplot(1, 3, 2)
    plt.boxplot(train_df['session_value'])
    plt.title('Session Value Box Plot')
    plt.ylabel('Session Value')

    plt.subplot(1, 3, 3)
    plt.hist(np.log1p(train_df['session_value']), bins=50, alpha=0.7, edgecolor='black')
    plt.title('Log(Session Value + 1) Distribution')
    plt.xlabel('Log(Session Value + 1)')
    plt.ylabel('Frequency')

    plt.tight_layout()
    plt.savefig('session_value_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Percentile analysis
    percentiles = [10, 25, 50, 75, 90, 95, 99]
    print(f"\nSession Value Percentiles:")
    for p in percentiles:
        value = np.percentile(train_df['session_value'], p)
        print(f"{p}th percentile: {value:.2f}")

def analyze_event_types(train_df):
    """Analyze event types and their relationship with session value"""
    print("\n" + "=" * 60)
    print("EVENT TYPE ANALYSIS")
    print("=" * 60)

    # Event type distribution
    event_counts = train_df['event_type'].value_counts()
    print("Event Type Distribution:")
    print(event_counts)
    print(f"\nEvent Type Percentages:")
    print((event_counts / len(train_df) * 100).round(2))

    # Session value by event type
    event_session_stats = train_df.groupby('event_type')['session_value'].agg([
        'count', 'mean', 'median', 'std', 'min', 'max'
    ]).round(2)
    print(f"\nSession Value Statistics by Event Type:")
    print(event_session_stats)

    # Visualization
    plt.figure(figsize=(15, 10))

    plt.subplot(2, 2, 1)
    event_counts.plot(kind='bar')
    plt.title('Event Type Distribution')
    plt.xlabel('Event Type')
    plt.ylabel('Count')
    plt.xticks(rotation=45)

    plt.subplot(2, 2, 2)
    train_df.boxplot(column='session_value', by='event_type', ax=plt.gca())
    plt.title('Session Value by Event Type')
    plt.suptitle('')
    plt.xticks(rotation=45)

    plt.subplot(2, 2, 3)
    event_session_stats['mean'].plot(kind='bar')
    plt.title('Average Session Value by Event Type')
    plt.xlabel('Event Type')
    plt.ylabel('Average Session Value')
    plt.xticks(rotation=45)

    plt.subplot(2, 2, 4)
    # Event type pie chart
    plt.pie(event_counts.values, labels=event_counts.index, autopct='%1.1f%%')
    plt.title('Event Type Distribution (Pie Chart)')

    plt.tight_layout()
    plt.savefig('event_type_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def analyze_sessions_and_users(train_df):
    """Analyze user sessions and user behavior"""
    print("\n" + "=" * 60)
    print("SESSION AND USER ANALYSIS")
    print("=" * 60)

    # Basic counts
    unique_sessions = train_df['user_session'].nunique()
    unique_users = train_df['user_id'].nunique()
    unique_products = train_df['product_id'].nunique()
    unique_categories = train_df['category_id'].nunique()

    print(f"Unique user sessions: {unique_sessions:,}")
    print(f"Unique users: {unique_users:,}")
    print(f"Unique products: {unique_products:,}")
    print(f"Unique categories: {unique_categories:,}")
    print(f"Total events: {len(train_df):,}")

    # Events per session
    events_per_session = train_df.groupby('user_session').size()
    print(f"\nEvents per session statistics:")
    print(f"Mean: {events_per_session.mean():.2f}")
    print(f"Median: {events_per_session.median():.2f}")
    print(f"Min: {events_per_session.min()}")
    print(f"Max: {events_per_session.max()}")

    # Sessions per user
    sessions_per_user = train_df.groupby('user_id')['user_session'].nunique()
    print(f"\nSessions per user statistics:")
    print(f"Mean: {sessions_per_user.mean():.2f}")
    print(f"Median: {sessions_per_user.median():.2f}")
    print(f"Min: {sessions_per_user.min()}")
    print(f"Max: {sessions_per_user.max()}")

    # Session value per session (should be constant per session)
    session_values = train_df.groupby('user_session')['session_value'].agg(['mean', 'std', 'count'])
    print(f"\nSession value consistency check:")
    print(f"Sessions with varying session_value: {(session_values['std'] > 0).sum()}")

    # Visualizations
    plt.figure(figsize=(15, 10))

    plt.subplot(2, 2, 1)
    plt.hist(events_per_session, bins=50, alpha=0.7, edgecolor='black')
    plt.title('Events per Session Distribution')
    plt.xlabel('Number of Events')
    plt.ylabel('Frequency')

    plt.subplot(2, 2, 2)
    plt.hist(sessions_per_user, bins=50, alpha=0.7, edgecolor='black')
    plt.title('Sessions per User Distribution')
    plt.xlabel('Number of Sessions')
    plt.ylabel('Frequency')

    plt.subplot(2, 2, 3)
    # Session value vs events per session
    session_event_counts = train_df.groupby('user_session').agg({
        'session_value': 'first',
        'event_time': 'count'
    }).rename(columns={'event_time': 'event_count'})

    plt.scatter(session_event_counts['event_count'], session_event_counts['session_value'], alpha=0.5)
    plt.xlabel('Events per Session')
    plt.ylabel('Session Value')
    plt.title('Session Value vs Events per Session')

    plt.subplot(2, 2, 4)
    # Top categories by session value
    category_stats = train_df.groupby('category_id')['session_value'].mean().sort_values(ascending=False).head(10)
    category_stats.plot(kind='bar')
    plt.title('Top 10 Categories by Average Session Value')
    plt.xlabel('Category ID')
    plt.ylabel('Average Session Value')
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig('session_user_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    return events_per_session, sessions_per_user

def analyze_temporal_patterns(train_df):
    """Analyze temporal patterns in the data"""
    print("\n" + "=" * 60)
    print("TEMPORAL PATTERN ANALYSIS")
    print("=" * 60)

    # Convert event_time to datetime
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])

    # Extract temporal features
    train_df['hour'] = train_df['event_time'].dt.hour
    train_df['day_of_week'] = train_df['event_time'].dt.dayofweek
    train_df['date'] = train_df['event_time'].dt.date

    print(f"Date range: {train_df['event_time'].min()} to {train_df['event_time'].max()}")
    print(f"Total days: {train_df['date'].nunique()}")

    # Hourly patterns
    hourly_stats = train_df.groupby('hour').agg({
        'session_value': ['mean', 'count'],
        'user_session': 'nunique'
    }).round(2)

    print(f"\nHourly patterns (top 5 hours by session value):")
    hourly_means = train_df.groupby('hour')['session_value'].mean().sort_values(ascending=False)
    print(hourly_means.head())

    # Daily patterns
    daily_stats = train_df.groupby('day_of_week').agg({
        'session_value': ['mean', 'count'],
        'user_session': 'nunique'
    }).round(2)

    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    daily_means = train_df.groupby('day_of_week')['session_value'].mean()

    print(f"\nDaily patterns:")
    for i, day in enumerate(day_names):
        print(f"{day}: {daily_means.iloc[i]:.2f}")

    # Visualizations
    plt.figure(figsize=(15, 10))

    plt.subplot(2, 2, 1)
    hourly_means.plot(kind='bar')
    plt.title('Average Session Value by Hour')
    plt.xlabel('Hour of Day')
    plt.ylabel('Average Session Value')

    plt.subplot(2, 2, 2)
    daily_means.plot(kind='bar')
    plt.title('Average Session Value by Day of Week')
    plt.xlabel('Day of Week (0=Monday)')
    plt.ylabel('Average Session Value')

    plt.subplot(2, 2, 3)
    # Events over time
    daily_events = train_df.groupby('date').size()
    daily_events.plot()
    plt.title('Daily Event Count Over Time')
    plt.xlabel('Date')
    plt.ylabel('Number of Events')
    plt.xticks(rotation=45)

    plt.subplot(2, 2, 4)
    # Session values over time
    daily_session_values = train_df.groupby('date')['session_value'].mean()
    daily_session_values.plot()
    plt.title('Average Session Value Over Time')
    plt.xlabel('Date')
    plt.ylabel('Average Session Value')
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig('temporal_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    return train_df

def analyze_correlations_and_missing_data(train_df):
    """Analyze correlations and missing data"""
    print("\n" + "=" * 60)
    print("CORRELATION AND MISSING DATA ANALYSIS")
    print("=" * 60)

    # Missing data analysis
    print("Missing data analysis:")
    missing_data = train_df.isnull().sum()
    print(missing_data[missing_data > 0])

    if missing_data.sum() == 0:
        print("No missing data found!")

    # Data types
    print(f"\nData types:")
    print(train_df.dtypes)

    # Check for duplicates
    duplicates = train_df.duplicated().sum()
    print(f"\nDuplicate rows: {duplicates}")

    # Session-level analysis
    session_level_data = train_df.groupby('user_session').agg({
        'session_value': 'first',
        'user_id': 'first',
        'event_type': lambda x: list(x),
        'product_id': 'nunique',
        'category_id': 'nunique',
        'event_time': ['count', 'min', 'max']
    })

    # Flatten column names
    session_level_data.columns = ['session_value', 'user_id', 'event_types',
                                  'unique_products', 'unique_categories',
                                  'event_count', 'first_event', 'last_event']

    # Calculate session duration
    session_level_data['session_duration'] = (
        pd.to_datetime(session_level_data['last_event']) -
        pd.to_datetime(session_level_data['first_event'])
    ).dt.total_seconds() / 60  # in minutes

    print(f"\nSession-level statistics:")
    print(f"Average session duration: {session_level_data['session_duration'].mean():.2f} minutes")
    print(f"Average events per session: {session_level_data['event_count'].mean():.2f}")
    print(f"Average unique products per session: {session_level_data['unique_products'].mean():.2f}")
    print(f"Average unique categories per session: {session_level_data['unique_categories'].mean():.2f}")

    return session_level_data

def main():
    """Main execution function"""
    print("Starting E-commerce Session Value Prediction - Data Exploration")
    print("=" * 80)

    # Load data and basic info
    train_df, test_df, sample_sub = load_and_basic_info()

    # Analyze target variable
    analyze_target_variable(train_df)

    # Analyze event types
    analyze_event_types(train_df)

    # Analyze sessions and users
    events_per_session, sessions_per_user = analyze_sessions_and_users(train_df)

    # Analyze temporal patterns
    train_df = analyze_temporal_patterns(train_df)

    # Analyze correlations and missing data
    session_level_data = analyze_correlations_and_missing_data(train_df)

    print("\n" + "=" * 80)
    print("DATA EXPLORATION COMPLETED")
    print("Generated files:")
    print("- session_value_analysis.png")
    print("- event_type_analysis.png")
    print("- session_user_analysis.png")
    print("- temporal_analysis.png")
    print("=" * 80)

    return train_df, test_df, sample_sub, session_level_data

if __name__ == "__main__":
    train_df, test_df, sample_sub, session_level_data = main()