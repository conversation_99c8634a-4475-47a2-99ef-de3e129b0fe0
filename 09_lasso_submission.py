#!/usr/bin/env python3
"""
Lasso Regression Model for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import Lasso, LassoCV
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()
    test_sessions = pd.read_csv('test_sessions.csv')

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")

    return X_train, X_test, y_train, test_sessions

def train_lasso_model(X_train, y_train):
    """Train Lasso Regression model with optimized alpha"""
    print("\n" + "=" * 60)
    print("TRAINING LASSO REGRESSION MODEL")
    print("=" * 60)

    # Scale the features (important for Lasso Regression)
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # Split for validation
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train_scaled, y_train, test_size=0.2, random_state=42
    )

    # Use LassoCV to find optimal alpha with cross-validation
    alphas = [0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 50.0, 100.0]

    print("Finding optimal alpha with cross-validation...")
    lasso_cv = LassoCV(alphas=alphas, cv=5, random_state=42, max_iter=2000)
    lasso_cv.fit(X_train_split, y_train_split)

    optimal_alpha = lasso_cv.alpha_
    print(f"Optimal alpha: {optimal_alpha}")

    # Train Lasso with optimal alpha
    lasso_model = Lasso(alpha=optimal_alpha, random_state=42, max_iter=2000)
    lasso_model.fit(X_train_split, y_train_split)

    # Evaluate on validation set
    y_val_pred = lasso_model.predict(X_val_split)
    val_mse = mean_squared_error(y_val_split, y_val_pred)
    val_r2 = r2_score(y_val_split, y_val_pred)

    print(f"Validation MSE: {val_mse:.4f}")
    print(f"Validation R²: {val_r2:.4f}")

    # Cross-validation score
    cv_scores = cross_val_score(lasso_model, X_train_scaled, y_train, cv=5,
                               scoring='neg_mean_squared_error')
    cv_mse = -cv_scores.mean()
    cv_std = cv_scores.std()

    print(f"Cross-validation MSE: {cv_mse:.4f} (±{cv_std:.4f})")

    # Train final model on full training data
    print("\nTraining final model on full training data...")
    final_model = Lasso(alpha=optimal_alpha, random_state=42, max_iter=2000)
    final_model.fit(X_train_scaled, y_train)

    # Final training evaluation
    y_train_pred = final_model.predict(X_train_scaled)
    train_mse = mean_squared_error(y_train, y_train_pred)
    train_r2 = r2_score(y_train, y_train_pred)

    print(f"Final training MSE: {train_mse:.4f}")
    print(f"Final training R²: {train_r2:.4f}")

    return final_model, scaler, optimal_alpha

def analyze_feature_selection(model, feature_names):
    """Analyze Lasso feature selection and coefficients"""
    print("\n" + "=" * 60)
    print("LASSO FEATURE SELECTION ANALYSIS")
    print("=" * 60)

    # Get coefficients
    coefficients = model.coef_

    # Identify selected features (non-zero coefficients)
    selected_features = coefficients != 0
    selected_count = selected_features.sum()
    total_features = len(coefficients)

    print(f"Feature Selection Results:")
    print(f"Total features: {total_features}")
    print(f"Selected features: {selected_count}")
    print(f"Eliminated features: {total_features - selected_count}")
    print(f"Selection ratio: {selected_count/total_features:.2%}")

    # Create feature importance DataFrame
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'coefficient': coefficients,
        'abs_coefficient': np.abs(coefficients),
        'selected': selected_features
    }).sort_values('abs_coefficient', ascending=False)

    # Show selected features
    selected_features_df = feature_importance_df[feature_importance_df['selected']].copy()

    print(f"\nTop Selected Features by Absolute Coefficient:")
    print(selected_features_df[['feature', 'coefficient', 'abs_coefficient']].head(20).to_string(index=False, float_format='%.4f'))

    # Show eliminated features
    eliminated_features_df = feature_importance_df[~feature_importance_df['selected']].copy()

    if len(eliminated_features_df) > 0:
        print(f"\nEliminated Features (coefficient = 0):")
        print(f"Count: {len(eliminated_features_df)}")
        print("Features:", eliminated_features_df['feature'].tolist()[:10], "..." if len(eliminated_features_df) > 10 else "")

    # Save feature analysis
    feature_importance_df.to_csv('lasso_feature_analysis.csv', index=False)
    print(f"\nFeature analysis saved to 'lasso_feature_analysis.csv'")

    # Analyze positive vs negative coefficients
    positive_coefs = selected_features_df[selected_features_df['coefficient'] > 0]
    negative_coefs = selected_features_df[selected_features_df['coefficient'] < 0]

    print(f"\nSelected Coefficient Analysis:")
    print(f"Positive coefficients: {len(positive_coefs)} features")
    print(f"Negative coefficients: {len(negative_coefs)} features")
    print(f"Intercept: {model.intercept_:.4f}")

    return feature_importance_df, selected_features_df

def make_predictions(model, scaler, X_test):
    """Make predictions on test data"""
    print("\n" + "=" * 60)
    print("MAKING PREDICTIONS ON TEST DATA")
    print("=" * 60)

    # Scale test features
    X_test_scaled = scaler.transform(X_test)

    # Make predictions
    predictions = model.predict(X_test_scaled)

    print(f"Generated {len(predictions)} predictions")
    print(f"Prediction statistics:")
    print(f"  Mean: {predictions.mean():.4f}")
    print(f"  Median: {np.median(predictions):.4f}")
    print(f"  Min: {predictions.min():.4f}")
    print(f"  Max: {predictions.max():.4f}")
    print(f"  Std: {predictions.std():.4f}")

    return predictions

def create_submission_file(predictions, test_sessions):
    """Create the final submission file"""
    print("\n" + "=" * 60)
    print("CREATING LASSO REGRESSION SUBMISSION FILE")
    print("=" * 60)

    # Create submission DataFrame
    submission = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': predictions
    })

    # Ensure predictions are positive (Lasso can predict negative values)
    submission['session_value'] = np.maximum(submission['session_value'], 0.1)

    # Sort by user_session
    submission = submission.sort_values('user_session').reset_index(drop=True)

    # Save submission file
    submission.to_csv('lasso_submission.csv', index=False)

    print(f"Lasso Regression submission file saved as 'lasso_submission.csv'")
    print(f"First few predictions:")
    print(submission.head(10))

    # Check how many predictions were clipped to positive
    clipped_count = (predictions <= 0).sum()
    if clipped_count > 0:
        print(f"\nNote: {clipped_count} negative predictions were clipped to 0.1")

    return submission

def main():
    """Main Lasso Regression pipeline"""
    print("Starting Lasso Regression Model Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train, test_sessions = load_processed_data()

    # Train Lasso Regression model
    lasso_model, scaler, optimal_alpha = train_lasso_model(X_train, y_train)

    # Analyze feature selection and coefficients
    feature_importance_df, selected_features_df = analyze_feature_selection(lasso_model, X_train.columns)

    # Make predictions on test data
    predictions = make_predictions(lasso_model, scaler, X_test)

    # Create submission file
    submission = create_submission_file(predictions, test_sessions)

    print("\n" + "=" * 80)
    print("LASSO REGRESSION MODEL COMPLETED")
    print(f"📁 File to submit: lasso_submission.csv")
    print(f"📊 Feature analysis: lasso_feature_analysis.csv")
    print(f"🎯 Optimal alpha: {optimal_alpha}")
    print(f"🔍 Selected features: {len(selected_features_df)}/{len(X_train.columns)}")
    print("=" * 80)

    return lasso_model, submission, feature_importance_df, selected_features_df, optimal_alpha

if __name__ == "__main__":
    lasso_model, submission, feature_importance_df, selected_features_df, optimal_alpha = main()