#!/usr/bin/env python3
"""
Random Forest Model for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()
    test_sessions = pd.read_csv('test_sessions.csv')

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")

    return X_train, X_test, y_train, test_sessions

def train_random_forest_model(X_train, y_train):
    """Train Random Forest model with optimized parameters"""
    print("\n" + "=" * 60)
    print("TRAINING RANDOM FOREST MODEL")
    print("=" * 60)

    # Split for validation
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )

    # First, train a basic Random Forest to get baseline
    print("Training baseline Random Forest...")
    rf_baseline = RandomForestRegressor(
        n_estimators=100,
        random_state=42,
        n_jobs=-1
    )
    rf_baseline.fit(X_train_split, y_train_split)

    # Evaluate baseline
    y_val_pred_baseline = rf_baseline.predict(X_val_split)
    val_mse_baseline = mean_squared_error(y_val_split, y_val_pred_baseline)
    val_r2_baseline = r2_score(y_val_split, y_val_pred_baseline)

    print(f"Baseline - Validation MSE: {val_mse_baseline:.4f}")
    print(f"Baseline - Validation R²: {val_r2_baseline:.4f}")

    # Hyperparameter tuning with GridSearchCV
    print("\nPerforming hyperparameter tuning...")
    param_grid = {
        'n_estimators': [200, 300, 500],
        'max_depth': [10, 15, 20, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4],
        'max_features': ['sqrt', 'log2', None]
    }

    # Use a smaller grid for faster execution
    param_grid_small = {
        'n_estimators': [200, 300],
        'max_depth': [15, 20],
        'min_samples_split': [2, 5],
        'min_samples_leaf': [1, 2],
        'max_features': ['sqrt', None]
    }

    rf_grid = RandomForestRegressor(random_state=42, n_jobs=-1)

    grid_search = GridSearchCV(
        rf_grid, param_grid_small, cv=3, scoring='neg_mean_squared_error',
        n_jobs=-1, verbose=1
    )

    grid_search.fit(X_train_split, y_train_split)

    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Best CV score: {-grid_search.best_score_:.4f}")

    # Train final model with best parameters
    best_rf = grid_search.best_estimator_

    # Evaluate on validation set
    y_val_pred = best_rf.predict(X_val_split)
    val_mse = mean_squared_error(y_val_split, y_val_pred)
    val_r2 = r2_score(y_val_split, y_val_pred)

    print(f"Tuned model - Validation MSE: {val_mse:.4f}")
    print(f"Tuned model - Validation R²: {val_r2:.4f}")

    # Cross-validation score
    cv_scores = cross_val_score(best_rf, X_train, y_train, cv=5,
                               scoring='neg_mean_squared_error', n_jobs=-1)
    cv_mse = -cv_scores.mean()
    cv_std = cv_scores.std()

    print(f"Cross-validation MSE: {cv_mse:.4f} (±{cv_std:.4f})")

    # Train final model on full training data
    print("\nTraining final model on full training data...")
    final_model = RandomForestRegressor(**grid_search.best_params_, random_state=42, n_jobs=-1)
    final_model.fit(X_train, y_train)

    # Final training evaluation
    y_train_pred = final_model.predict(X_train)
    train_mse = mean_squared_error(y_train, y_train_pred)
    train_r2 = r2_score(y_train, y_train_pred)

    print(f"Final training MSE: {train_mse:.4f}")
    print(f"Final training R²: {train_r2:.4f}")

    return final_model, grid_search.best_params_

def analyze_feature_importance(model, feature_names):
    """Analyze Random Forest feature importance"""
    print("\n" + "=" * 60)
    print("RANDOM FOREST FEATURE IMPORTANCE ANALYSIS")
    print("=" * 60)

    # Get feature importance
    importance = model.feature_importances_

    # Create feature importance DataFrame
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    }).sort_values('importance', ascending=False)

    print("Top 20 Most Important Features:")
    print(feature_importance_df.head(20).to_string(index=False, float_format='%.4f'))

    # Save feature importance
    feature_importance_df.to_csv('random_forest_feature_importance.csv', index=False)
    print("\nFeature importance saved to 'random_forest_feature_importance.csv'")

    # Analyze feature importance distribution
    total_importance = feature_importance_df['importance'].sum()
    cumulative_importance = feature_importance_df['importance'].cumsum()

    # Find how many features contribute to 80% and 95% of importance
    features_80 = (cumulative_importance <= 0.8).sum() + 1
    features_95 = (cumulative_importance <= 0.95).sum() + 1

    print(f"\nFeature Importance Analysis:")
    print(f"Top {features_80} features contribute to 80% of importance")
    print(f"Top {features_95} features contribute to 95% of importance")
    print(f"Most important feature: {feature_importance_df.iloc[0]['feature']} ({feature_importance_df.iloc[0]['importance']:.4f})")

    return feature_importance_df

def make_predictions(model, X_test):
    """Make predictions on test data"""
    print("\n" + "=" * 60)
    print("MAKING PREDICTIONS ON TEST DATA")
    print("=" * 60)

    # Make predictions
    predictions = model.predict(X_test)

    print(f"Generated {len(predictions)} predictions")
    print(f"Prediction statistics:")
    print(f"  Mean: {predictions.mean():.4f}")
    print(f"  Median: {np.median(predictions):.4f}")
    print(f"  Min: {predictions.min():.4f}")
    print(f"  Max: {predictions.max():.4f}")
    print(f"  Std: {predictions.std():.4f}")

    return predictions

def create_submission_file(predictions, test_sessions):
    """Create the final submission file"""
    print("\n" + "=" * 60)
    print("CREATING RANDOM FOREST SUBMISSION FILE")
    print("=" * 60)

    # Create submission DataFrame
    submission = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': predictions
    })

    # Random Forest typically doesn't predict negative values, but let's be safe
    submission['session_value'] = np.maximum(submission['session_value'], 0.1)

    # Sort by user_session
    submission = submission.sort_values('user_session').reset_index(drop=True)

    # Save submission file
    submission.to_csv('random_forest_submission.csv', index=False)

    print(f"Random Forest submission file saved as 'random_forest_submission.csv'")
    print(f"First few predictions:")
    print(submission.head(10))

    # Check how many predictions were clipped to positive
    clipped_count = (predictions <= 0).sum()
    if clipped_count > 0:
        print(f"\nNote: {clipped_count} negative predictions were clipped to 0.1")
    else:
        print("\nAll predictions were positive (as expected for Random Forest)")

    return submission

def analyze_model_performance(model, X_train, y_train):
    """Analyze model performance and characteristics"""
    print("\n" + "=" * 60)
    print("MODEL PERFORMANCE ANALYSIS")
    print("=" * 60)

    # Model characteristics
    print(f"Model Parameters:")
    print(f"  Number of trees: {model.n_estimators}")
    print(f"  Max depth: {model.max_depth}")
    print(f"  Min samples split: {model.min_samples_split}")
    print(f"  Min samples leaf: {model.min_samples_leaf}")
    print(f"  Max features: {model.max_features}")

    # Out-of-bag score if available
    if hasattr(model, 'oob_score_') and model.oob_score_ is not None:
        print(f"  OOB Score: {model.oob_score_:.4f}")

    # Feature importance statistics
    importances = model.feature_importances_
    print(f"\nFeature Importance Statistics:")
    print(f"  Mean importance: {importances.mean():.4f}")
    print(f"  Std importance: {importances.std():.4f}")
    print(f"  Max importance: {importances.max():.4f}")
    print(f"  Min importance: {importances.min():.4f}")

def main():
    """Main Random Forest pipeline"""
    print("Starting Random Forest Model Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train, test_sessions = load_processed_data()

    # Train Random Forest model
    rf_model, best_params = train_random_forest_model(X_train, y_train)

    # Analyze feature importance
    feature_importance_df = analyze_feature_importance(rf_model, X_train.columns)

    # Analyze model performance
    analyze_model_performance(rf_model, X_train, y_train)

    # Make predictions on test data
    predictions = make_predictions(rf_model, X_test)

    # Create submission file
    submission = create_submission_file(predictions, test_sessions)

    print("\n" + "=" * 80)
    print("RANDOM FOREST MODEL COMPLETED")
    print(f"📁 File to submit: random_forest_submission.csv")
    print(f"📊 Feature importance: random_forest_feature_importance.csv")
    print(f"🌳 Best parameters: {best_params}")
    print("=" * 80)

    return rf_model, submission, feature_importance_df, best_params

if __name__ == "__main__":
    rf_model, submission, feature_importance_df, best_params = main()