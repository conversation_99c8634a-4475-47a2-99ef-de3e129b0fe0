#!/usr/bin/env python3
"""
LightGBM Model for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()
    test_sessions = pd.read_csv('test_sessions.csv')

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")

    return X_train, X_test, y_train, test_sessions

def train_lightgbm_model(X_train, y_train):
    """Train LightGBM model with optimized parameters"""
    print("\n" + "=" * 60)
    print("TRAINING LIGHTGBM MODEL")
    print("=" * 60)

    # Split for validation
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )

    # Create LightGBM datasets
    train_data = lgb.Dataset(X_train_split, label=y_train_split)
    val_data = lgb.Dataset(X_val_split, label=y_val_split, reference=train_data)

    # LightGBM parameters
    params = {
        'objective': 'regression',
        'metric': 'rmse',
        'boosting_type': 'gbdt',
        'num_leaves': 63,
        'max_depth': 6,
        'learning_rate': 0.1,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'reg_alpha': 0.1,
        'reg_lambda': 1.0,
        'min_child_samples': 20,
        'random_state': 42,
        'n_jobs': -1,
        'verbose': -1
    }

    print("Training LightGBM with early stopping...")

    # Train with early stopping
    lgb_model = lgb.train(
        params,
        train_data,
        valid_sets=[val_data],
        num_boost_round=1000,
        callbacks=[
            lgb.early_stopping(stopping_rounds=50),
            lgb.log_evaluation(period=0)  # Silent training
        ]
    )

    # Evaluate on validation set
    y_val_pred = lgb_model.predict(X_val_split, num_iteration=lgb_model.best_iteration)
    val_mse = mean_squared_error(y_val_split, y_val_pred)
    val_r2 = r2_score(y_val_split, y_val_pred)

    print(f"Validation MSE: {val_mse:.4f}")
    print(f"Validation R²: {val_r2:.4f}")
    print(f"Best iteration: {lgb_model.best_iteration}")

    # Train final model on full training data
    print("\nTraining final model on full training data...")
    full_train_data = lgb.Dataset(X_train, label=y_train)

    final_model = lgb.train(
        params,
        full_train_data,
        num_boost_round=lgb_model.best_iteration,
        callbacks=[lgb.log_evaluation(period=0)]
    )

    # Final training evaluation
    y_train_pred = final_model.predict(X_train, num_iteration=final_model.best_iteration)
    train_mse = mean_squared_error(y_train, y_train_pred)
    train_r2 = r2_score(y_train, y_train_pred)

    print(f"Final training MSE: {train_mse:.4f}")
    print(f"Final training R²: {train_r2:.4f}")

    return final_model

def analyze_feature_importance(model, feature_names):
    """Analyze LightGBM feature importance"""
    print("\n" + "=" * 60)
    print("FEATURE IMPORTANCE ANALYSIS")
    print("=" * 60)

    # Get feature importance (gain-based)
    importance_gain = model.feature_importance(importance_type='gain')

    # Normalize importance
    importance_gain = importance_gain / importance_gain.sum()

    # Create feature importance DataFrame
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance_gain
    }).sort_values('importance', ascending=False)

    print("Top 20 Most Important Features:")
    print(feature_importance_df.head(20).to_string(index=False, float_format='%.4f'))

    # Save feature importance
    feature_importance_df.to_csv('lightgbm_feature_importance.csv', index=False)
    print("\nFeature importance saved to 'lightgbm_feature_importance.csv'")

    return feature_importance_df

def make_predictions(model, X_test):
    """Make predictions on test data"""
    print("\n" + "=" * 60)
    print("MAKING PREDICTIONS ON TEST DATA")
    print("=" * 60)

    # Make predictions
    predictions = model.predict(X_test, num_iteration=model.best_iteration)

    print(f"Generated {len(predictions)} predictions")
    print(f"Prediction statistics:")
    print(f"  Mean: {predictions.mean():.4f}")
    print(f"  Median: {np.median(predictions):.4f}")
    print(f"  Min: {predictions.min():.4f}")
    print(f"  Max: {predictions.max():.4f}")
    print(f"  Std: {predictions.std():.4f}")

    return predictions

def create_submission_file(predictions, test_sessions):
    """Create the final submission file"""
    print("\n" + "=" * 60)
    print("CREATING LIGHTGBM SUBMISSION FILE")
    print("=" * 60)

    # Create submission DataFrame
    submission = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': predictions
    })

    # Ensure predictions are positive
    submission['session_value'] = np.maximum(submission['session_value'], 0.1)

    # Sort by user_session
    submission = submission.sort_values('user_session').reset_index(drop=True)

    # Save submission file
    submission.to_csv('lightgbm_submission.csv', index=False)

    print(f"LightGBM submission file saved as 'lightgbm_submission.csv'")
    print(f"First few predictions:")
    print(submission.head(10))

    return submission

def main():
    """Main LightGBM pipeline"""
    print("Starting LightGBM Model Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train, test_sessions = load_processed_data()

    # Train LightGBM model
    lgb_model = train_lightgbm_model(X_train, y_train)

    # Analyze feature importance
    feature_importance_df = analyze_feature_importance(lgb_model, X_train.columns)

    # Make predictions on test data
    predictions = make_predictions(lgb_model, X_test)

    # Create submission file
    submission = create_submission_file(predictions, test_sessions)

    print("\n" + "=" * 80)
    print("LIGHTGBM MODEL COMPLETED")
    print("📁 File to submit: lightgbm_submission.csv")
    print("📊 Feature importance: lightgbm_feature_importance.csv")
    print("=" * 80)

    return lgb_model, submission, feature_importance_df

if __name__ == "__main__":
    lgb_model, submission, feature_importance_df = main()