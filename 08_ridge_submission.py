#!/usr/bin/env python3
"""
Ridge Regression Model for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import Ridge, RidgeCV
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()
    test_sessions = pd.read_csv('test_sessions.csv')

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")

    return X_train, X_test, y_train, test_sessions

def train_ridge_model(X_train, y_train):
    """Train Ridge Regression model with optimized alpha"""
    print("\n" + "=" * 60)
    print("TRAINING RIDGE REGRESSION MODEL")
    print("=" * 60)

    # Scale the features (important for Ridge Regression)
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # Split for validation
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train_scaled, y_train, test_size=0.2, random_state=42
    )

    # Use RidgeCV to find optimal alpha with cross-validation
    alphas = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 50.0, 100.0, 200.0, 500.0]

    print("Finding optimal alpha with cross-validation...")
    ridge_cv = RidgeCV(alphas=alphas, cv=5, scoring='neg_mean_squared_error')
    ridge_cv.fit(X_train_split, y_train_split)

    optimal_alpha = ridge_cv.alpha_
    print(f"Optimal alpha: {optimal_alpha}")

    # Train Ridge with optimal alpha
    ridge_model = Ridge(alpha=optimal_alpha, random_state=42)
    ridge_model.fit(X_train_split, y_train_split)

    # Evaluate on validation set
    y_val_pred = ridge_model.predict(X_val_split)
    val_mse = mean_squared_error(y_val_split, y_val_pred)
    val_r2 = r2_score(y_val_split, y_val_pred)

    print(f"Validation MSE: {val_mse:.4f}")
    print(f"Validation R²: {val_r2:.4f}")

    # Cross-validation score
    cv_scores = cross_val_score(ridge_model, X_train_scaled, y_train, cv=5,
                               scoring='neg_mean_squared_error')
    cv_mse = -cv_scores.mean()
    cv_std = cv_scores.std()

    print(f"Cross-validation MSE: {cv_mse:.4f} (±{cv_std:.4f})")

    # Train final model on full training data
    print("\nTraining final model on full training data...")
    final_model = Ridge(alpha=optimal_alpha, random_state=42)
    final_model.fit(X_train_scaled, y_train)

    # Final training evaluation
    y_train_pred = final_model.predict(X_train_scaled)
    train_mse = mean_squared_error(y_train, y_train_pred)
    train_r2 = r2_score(y_train, y_train_pred)

    print(f"Final training MSE: {train_mse:.4f}")
    print(f"Final training R²: {train_r2:.4f}")

    return final_model, scaler, optimal_alpha

def analyze_feature_coefficients(model, feature_names, scaler):
    """Analyze Ridge Regression coefficients"""
    print("\n" + "=" * 60)
    print("FEATURE COEFFICIENT ANALYSIS")
    print("=" * 60)

    # Get coefficients
    coefficients = model.coef_

    # Create feature importance DataFrame based on absolute coefficients
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'coefficient': coefficients,
        'abs_coefficient': np.abs(coefficients)
    }).sort_values('abs_coefficient', ascending=False)

    print("Top 20 Features by Absolute Coefficient:")
    print(feature_importance_df[['feature', 'coefficient', 'abs_coefficient']].head(20).to_string(index=False, float_format='%.4f'))

    # Save feature coefficients
    feature_importance_df.to_csv('ridge_feature_coefficients.csv', index=False)
    print("\nFeature coefficients saved to 'ridge_feature_coefficients.csv'")

    # Analyze positive vs negative coefficients
    positive_coefs = feature_importance_df[feature_importance_df['coefficient'] > 0]
    negative_coefs = feature_importance_df[feature_importance_df['coefficient'] < 0]

    print(f"\nCoefficient Analysis:")
    print(f"Positive coefficients: {len(positive_coefs)} features")
    print(f"Negative coefficients: {len(negative_coefs)} features")
    print(f"Intercept: {model.intercept_:.4f}")

    return feature_importance_df

def make_predictions(model, scaler, X_test):
    """Make predictions on test data"""
    print("\n" + "=" * 60)
    print("MAKING PREDICTIONS ON TEST DATA")
    print("=" * 60)

    # Scale test features
    X_test_scaled = scaler.transform(X_test)

    # Make predictions
    predictions = model.predict(X_test_scaled)

    print(f"Generated {len(predictions)} predictions")
    print(f"Prediction statistics:")
    print(f"  Mean: {predictions.mean():.4f}")
    print(f"  Median: {np.median(predictions):.4f}")
    print(f"  Min: {predictions.min():.4f}")
    print(f"  Max: {predictions.max():.4f}")
    print(f"  Std: {predictions.std():.4f}")

    return predictions

def create_submission_file(predictions, test_sessions):
    """Create the final submission file"""
    print("\n" + "=" * 60)
    print("CREATING RIDGE REGRESSION SUBMISSION FILE")
    print("=" * 60)

    # Create submission DataFrame
    submission = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': predictions
    })

    # Ensure predictions are positive (Ridge can predict negative values)
    submission['session_value'] = np.maximum(submission['session_value'], 0.1)

    # Sort by user_session
    submission = submission.sort_values('user_session').reset_index(drop=True)

    # Save submission file
    submission.to_csv('ridge_submission.csv', index=False)

    print(f"Ridge Regression submission file saved as 'ridge_submission.csv'")
    print(f"First few predictions:")
    print(submission.head(10))

    # Check how many predictions were clipped to positive
    clipped_count = (predictions <= 0).sum()
    if clipped_count > 0:
        print(f"\nNote: {clipped_count} negative predictions were clipped to 0.1")

    return submission

def main():
    """Main Ridge Regression pipeline"""
    print("Starting Ridge Regression Model Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train, test_sessions = load_processed_data()

    # Train Ridge Regression model
    ridge_model, scaler, optimal_alpha = train_ridge_model(X_train, y_train)

    # Analyze feature coefficients
    feature_importance_df = analyze_feature_coefficients(ridge_model, X_train.columns, scaler)

    # Make predictions on test data
    predictions = make_predictions(ridge_model, scaler, X_test)

    # Create submission file
    submission = create_submission_file(predictions, test_sessions)

    print("\n" + "=" * 80)
    print("RIDGE REGRESSION MODEL COMPLETED")
    print(f"📁 File to submit: ridge_submission.csv")
    print(f"📊 Feature coefficients: ridge_feature_coefficients.csv")
    print(f"🎯 Optimal alpha: {optimal_alpha}")
    print("=" * 80)

    return ridge_model, submission, feature_importance_df, optimal_alpha

if __name__ == "__main__":
    ridge_model, submission, feature_importance_df, optimal_alpha = main()