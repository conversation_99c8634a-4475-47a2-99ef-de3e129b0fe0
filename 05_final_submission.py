#!/usr/bin/env python3
"""
Final Predictions & Submission for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()
    test_sessions = pd.read_csv('test_sessions.csv')
    sample_submission = pd.read_csv('sample_submission.csv')

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")
    print(f"Test sessions shape: {test_sessions.shape}")
    print(f"Sample submission shape: {sample_submission.shape}")

    return X_train, X_test, y_train, test_sessions, sample_submission

def train_best_model(X_train, y_train):
    """Train the best performing model (Neural Network) on full training data"""
    print("\n" + "=" * 60)
    print("TRAINING BEST MODEL ON FULL DATA")
    print("=" * 60)

    # Scale the features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # Create and train the best Neural Network model
    # Using parameters that performed well in initial training
    best_model = MLPRegressor(
        hidden_layer_sizes=(128, 64, 32),
        activation='relu',
        solver='adam',
        alpha=0.001,
        batch_size='auto',
        learning_rate='constant',
        learning_rate_init=0.001,
        max_iter=1000,
        random_state=42,
        early_stopping=True,
        validation_fraction=0.1,
        n_iter_no_change=20
    )

    print("Training Neural Network on full training data...")
    best_model.fit(X_train_scaled, y_train)

    # Evaluate on training data
    y_train_pred = best_model.predict(X_train_scaled)
    train_mse = mean_squared_error(y_train, y_train_pred)
    train_r2 = r2_score(y_train, y_train_pred)

    print(f"Final model training MSE: {train_mse:.4f}")
    print(f"Final model training R²: {train_r2:.4f}")

    return best_model, scaler

def make_predictions(model, scaler, X_test):
    """Make predictions on test data"""
    print("\n" + "=" * 60)
    print("MAKING PREDICTIONS ON TEST DATA")
    print("=" * 60)

    # Scale test features
    X_test_scaled = scaler.transform(X_test)

    # Make predictions
    predictions = model.predict(X_test_scaled)

    print(f"Generated {len(predictions)} predictions")
    print(f"Prediction statistics:")
    print(f"  Mean: {predictions.mean():.4f}")
    print(f"  Median: {np.median(predictions):.4f}")
    print(f"  Min: {predictions.min():.4f}")
    print(f"  Max: {predictions.max():.4f}")
    print(f"  Std: {predictions.std():.4f}")

    return predictions

def create_submission_file(predictions, test_sessions, sample_submission):
    """Create the final submission file"""
    print("\n" + "=" * 60)
    print("CREATING SUBMISSION FILE")
    print("=" * 60)

    # Create submission DataFrame
    submission = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': predictions
    })

    # Ensure predictions are positive (session values should be positive)
    submission['session_value'] = np.maximum(submission['session_value'], 0.1)

    # Verify format matches sample submission
    print("Verifying submission format...")
    print(f"Submission shape: {submission.shape}")
    print(f"Sample submission shape: {sample_submission.shape}")
    print(f"Columns match: {list(submission.columns) == list(sample_submission.columns)}")

    # Check for any missing sessions
    sample_sessions = set(sample_submission['user_session'])
    submission_sessions = set(submission['user_session'])

    if sample_sessions == submission_sessions:
        print("✓ All required sessions are present")
    else:
        missing = sample_sessions - submission_sessions
        extra = submission_sessions - sample_sessions
        if missing:
            print(f"⚠ Missing sessions: {len(missing)}")
        if extra:
            print(f"⚠ Extra sessions: {len(extra)}")

    # Sort by user_session to match sample submission order
    submission = submission.sort_values('user_session').reset_index(drop=True)

    # Save submission file
    submission.to_csv('submission.csv', index=False)

    print(f"Submission file saved as 'submission.csv'")
    print(f"First few predictions:")
    print(submission.head(10))

    return submission

def validate_submission(submission, sample_submission):
    """Validate the submission file"""
    print("\n" + "=" * 60)
    print("VALIDATING SUBMISSION")
    print("=" * 60)

    # Check basic format
    checks = []

    # Check columns
    if list(submission.columns) == ['user_session', 'session_value']:
        checks.append("✓ Correct column names")
    else:
        checks.append("✗ Incorrect column names")

    # Check shape
    if submission.shape[0] == sample_submission.shape[0]:
        checks.append("✓ Correct number of rows")
    else:
        checks.append(f"✗ Wrong number of rows: {submission.shape[0]} vs {sample_submission.shape[0]}")

    # Check for missing values
    if submission.isnull().sum().sum() == 0:
        checks.append("✓ No missing values")
    else:
        checks.append("✗ Contains missing values")

    # Check session_value range
    if (submission['session_value'] >= 0).all():
        checks.append("✓ All session values are non-negative")
    else:
        checks.append("✗ Some session values are negative")

    # Check for reasonable values (based on training data analysis)
    if submission['session_value'].max() < 5000:  # Reasonable upper bound
        checks.append("✓ Session values are in reasonable range")
    else:
        checks.append("⚠ Some session values might be too high")

    print("Validation Results:")
    for check in checks:
        print(f"  {check}")

    return all("✓" in check for check in checks)

def main():
    """Main submission pipeline"""
    print("Starting Final Predictions & Submission Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train, test_sessions, sample_submission = load_processed_data()

    # Train best model on full training data
    best_model, scaler = train_best_model(X_train, y_train)

    # Make predictions on test data
    predictions = make_predictions(best_model, scaler, X_test)

    # Create submission file
    submission = create_submission_file(predictions, test_sessions, sample_submission)

    # Validate submission
    is_valid = validate_submission(submission, sample_submission)

    print("\n" + "=" * 80)
    print("FINAL SUBMISSION COMPLETED")
    if is_valid:
        print("✓ Submission file is ready for Kaggle!")
        print("📁 File to submit: submission.csv")
    else:
        print("⚠ Please check validation warnings before submitting")
    print("=" * 80)

    return submission

if __name__ == "__main__":
    submission = main()