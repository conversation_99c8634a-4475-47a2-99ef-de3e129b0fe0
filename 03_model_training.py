#!/usr/bin/env python3
"""
Model Selection & Training for E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """Load the processed features and targets"""
    print("=" * 60)
    print("LOADING PROCESSED DATA")
    print("=" * 60)

    X_train = pd.read_csv('X_train_features.csv', index_col=0)
    X_test = pd.read_csv('X_test_features.csv', index_col=0)
    y_train = pd.read_csv('y_train.csv', index_col=0).squeeze()

    print(f"Training features shape: {X_train.shape}")
    print(f"Test features shape: {X_test.shape}")
    print(f"Training targets shape: {y_train.shape}")

    return X_train, X_test, y_train

def prepare_data_for_modeling(X_train, X_test, y_train, scale_features=True):
    """Prepare data for modeling with optional scaling"""
    print("\nPreparing data for modeling...")

    # Split training data for validation
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=None
    )

    if scale_features:
        scaler = StandardScaler()
        X_train_split_scaled = scaler.fit_transform(X_train_split)
        X_val_split_scaled = scaler.transform(X_val_split)
        X_test_scaled = scaler.transform(X_test)

        # Convert back to DataFrame to maintain column names
        X_train_split_scaled = pd.DataFrame(X_train_split_scaled,
                                          columns=X_train_split.columns,
                                          index=X_train_split.index)
        X_val_split_scaled = pd.DataFrame(X_val_split_scaled,
                                        columns=X_val_split.columns,
                                        index=X_val_split.index)
        X_test_scaled = pd.DataFrame(X_test_scaled,
                                   columns=X_test.columns,
                                   index=X_test.index)

        return (X_train_split_scaled, X_val_split_scaled, X_test_scaled,
                y_train_split, y_val_split, scaler)
    else:
        return (X_train_split, X_val_split, X_test,
                y_train_split, y_val_split, None)

def evaluate_model(model, X_train, X_val, y_train, y_val, model_name):
    """Evaluate a model and return metrics"""
    print(f"\nTraining {model_name}...")

    # Train the model
    model.fit(X_train, y_train)

    # Make predictions
    y_train_pred = model.predict(X_train)
    y_val_pred = model.predict(X_val)

    # Calculate metrics
    train_mse = mean_squared_error(y_train, y_train_pred)
    val_mse = mean_squared_error(y_val, y_val_pred)
    train_mae = mean_absolute_error(y_train, y_train_pred)
    val_mae = mean_absolute_error(y_val, y_val_pred)
    train_r2 = r2_score(y_train, y_train_pred)
    val_r2 = r2_score(y_val, y_val_pred)

    # Cross-validation score
    cv_scores = cross_val_score(model, X_train, y_train, cv=5,
                               scoring='neg_mean_squared_error', n_jobs=-1)
    cv_mse = -cv_scores.mean()
    cv_std = cv_scores.std()

    results = {
        'model_name': model_name,
        'train_mse': train_mse,
        'val_mse': val_mse,
        'train_mae': train_mae,
        'val_mae': val_mae,
        'train_r2': train_r2,
        'val_r2': val_r2,
        'cv_mse': cv_mse,
        'cv_std': cv_std,
        'model': model
    }

    print(f"{model_name} Results:")
    print(f"  Train MSE: {train_mse:.4f}")
    print(f"  Val MSE: {val_mse:.4f}")
    print(f"  Val R²: {val_r2:.4f}")
    print(f"  CV MSE: {cv_mse:.4f} (±{cv_std:.4f})")

    return results

def train_linear_models(X_train, X_val, X_test, y_train, y_val):
    """Train linear regression models"""
    print("\n" + "=" * 60)
    print("TRAINING LINEAR MODELS")
    print("=" * 60)

    models = []

    # Ridge Regression
    ridge = Ridge(alpha=1.0, random_state=42)
    ridge_results = evaluate_model(ridge, X_train, X_val, y_train, y_val, "Ridge Regression")
    models.append(ridge_results)

    # Lasso Regression
    lasso = Lasso(alpha=1.0, random_state=42, max_iter=2000)
    lasso_results = evaluate_model(lasso, X_train, X_val, y_train, y_val, "Lasso Regression")
    models.append(lasso_results)

    # Elastic Net
    elastic_net = ElasticNet(alpha=1.0, l1_ratio=0.5, random_state=42, max_iter=2000)
    elastic_results = evaluate_model(elastic_net, X_train, X_val, y_train, y_val, "Elastic Net")
    models.append(elastic_results)

    return models

def train_tree_models(X_train, X_val, X_test, y_train, y_val):
    """Train tree-based models"""
    print("\n" + "=" * 60)
    print("TRAINING TREE-BASED MODELS")
    print("=" * 60)

    models = []

    # Random Forest
    rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rf_results = evaluate_model(rf, X_train, X_val, y_train, y_val, "Random Forest")
    models.append(rf_results)

    # Gradient Boosting
    gb = GradientBoostingRegressor(n_estimators=100, random_state=42)
    gb_results = evaluate_model(gb, X_train, X_val, y_train, y_val, "Gradient Boosting")
    models.append(gb_results)

    # XGBoost
    xgb_model = xgb.XGBRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    xgb_results = evaluate_model(xgb_model, X_train, X_val, y_train, y_val, "XGBoost")
    models.append(xgb_results)

    # LightGBM
    lgb_model = lgb.LGBMRegressor(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1)
    lgb_results = evaluate_model(lgb_model, X_train, X_val, y_train, y_val, "LightGBM")
    models.append(lgb_results)

    return models

def create_simple_neural_network(input_dim):
    """Create a simple neural network using sklearn MLPRegressor"""
    from sklearn.neural_network import MLPRegressor

    model = MLPRegressor(
        hidden_layer_sizes=(128, 64, 32),
        activation='relu',
        solver='adam',
        alpha=0.001,
        batch_size='auto',
        learning_rate='constant',
        learning_rate_init=0.001,
        max_iter=500,
        random_state=42,
        early_stopping=True,
        validation_fraction=0.1,
        n_iter_no_change=10
    )

    return model

def train_neural_network_models(X_train, X_val, X_test, y_train, y_val):
    """Train neural network models"""
    print("\n" + "=" * 60)
    print("TRAINING NEURAL NETWORK MODELS")
    print("=" * 60)

    models = []

    # Simple Neural Network
    nn_model = create_simple_neural_network(X_train.shape[1])
    nn_results = evaluate_model(nn_model, X_train, X_val, y_train, y_val, "Neural Network")
    models.append(nn_results)

    return models

def compare_models(all_models):
    """Compare all models and select the best one"""
    print("\n" + "=" * 60)
    print("MODEL COMPARISON")
    print("=" * 60)

    # Create comparison DataFrame
    comparison_data = []
    for model_result in all_models:
        comparison_data.append({
            'Model': model_result['model_name'],
            'Train MSE': model_result['train_mse'],
            'Val MSE': model_result['val_mse'],
            'Val R²': model_result['val_r2'],
            'CV MSE': model_result['cv_mse'],
            'CV Std': model_result['cv_std']
        })

    comparison_df = pd.DataFrame(comparison_data)
    comparison_df = comparison_df.sort_values('Val MSE')

    print("Model Performance Comparison (sorted by Validation MSE):")
    print(comparison_df.to_string(index=False, float_format='%.4f'))

    # Select best model
    best_model_result = min(all_models, key=lambda x: x['val_mse'])
    best_model = best_model_result['model']
    best_model_name = best_model_result['model_name']

    print(f"\nBest Model: {best_model_name}")
    print(f"Best Validation MSE: {best_model_result['val_mse']:.4f}")
    print(f"Best Validation R²: {best_model_result['val_r2']:.4f}")

    return best_model, best_model_name, comparison_df

def analyze_feature_importance(model, feature_names, model_name):
    """Analyze feature importance for tree-based models"""
    print(f"\n" + "=" * 60)
    print(f"FEATURE IMPORTANCE ANALYSIS - {model_name}")
    print("=" * 60)

    if hasattr(model, 'feature_importances_'):
        # Tree-based models
        importances = model.feature_importances_
        feature_importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importances
        }).sort_values('importance', ascending=False)

        print("Top 20 Most Important Features:")
        print(feature_importance_df.head(20).to_string(index=False, float_format='%.4f'))

        return feature_importance_df

    elif hasattr(model, 'coef_'):
        # Linear models
        coefficients = np.abs(model.coef_)
        feature_importance_df = pd.DataFrame({
            'feature': feature_names,
            'abs_coefficient': coefficients
        }).sort_values('abs_coefficient', ascending=False)

        print("Top 20 Features by Absolute Coefficient:")
        print(feature_importance_df.head(20).to_string(index=False, float_format='%.4f'))

        return feature_importance_df

    else:
        print("Feature importance not available for this model type.")
        return None

def main():
    """Main model training pipeline"""
    print("Starting Model Selection & Training Pipeline")
    print("=" * 80)

    # Load processed data
    X_train, X_test, y_train = load_processed_data()

    # Prepare data for linear models (with scaling)
    (X_train_scaled, X_val_scaled, X_test_scaled,
     y_train_split, y_val_split, scaler) = prepare_data_for_modeling(
        X_train, X_test, y_train, scale_features=True)

    # Prepare data for tree models (without scaling)
    (X_train_unscaled, X_val_unscaled, X_test_unscaled,
     y_train_split_tree, y_val_split_tree, _) = prepare_data_for_modeling(
        X_train, X_test, y_train, scale_features=False)

    # Train all models
    all_models = []

    # Linear models (use scaled data)
    linear_models = train_linear_models(X_train_scaled, X_val_scaled, X_test_scaled,
                                      y_train_split, y_val_split)
    all_models.extend(linear_models)

    # Tree-based models (use unscaled data)
    tree_models = train_tree_models(X_train_unscaled, X_val_unscaled, X_test_unscaled,
                                  y_train_split_tree, y_val_split_tree)
    all_models.extend(tree_models)

    # Neural network models (use scaled data)
    nn_models = train_neural_network_models(X_train_scaled, X_val_scaled, X_test_scaled,
                                          y_train_split, y_val_split)
    all_models.extend(nn_models)

    # Compare models and select best
    best_model, best_model_name, comparison_df = compare_models(all_models)

    # Analyze feature importance
    feature_importance_df = analyze_feature_importance(best_model, X_train.columns, best_model_name)

    # Save results
    print("\n" + "=" * 60)
    print("SAVING RESULTS")
    print("=" * 60)

    comparison_df.to_csv('model_comparison.csv', index=False)
    if feature_importance_df is not None:
        feature_importance_df.to_csv('feature_importance.csv', index=False)

    print("Results saved:")
    print("- model_comparison.csv")
    if feature_importance_df is not None:
        print("- feature_importance.csv")

    print("\n" + "=" * 80)
    print("MODEL TRAINING COMPLETED")
    print(f"Best Model: {best_model_name}")
    print("=" * 80)

    return best_model, best_model_name, all_models, X_train, X_test, y_train

if __name__ == "__main__":
    best_model, best_model_name, all_models, X_train, X_test, y_train = main()